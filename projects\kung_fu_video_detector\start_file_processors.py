#!/usr/bin/env python3
"""
Start both file processors for the Kung Fu Video Detection system.
Runs vision processor and FFmpeg processor in parallel.
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def start_processors():
    """Start both file processors."""
    print("🚀 Starting Kung Fu Video Detection File Processors")
    print("=" * 50)
    
    # Get current directory
    current_dir = Path(__file__).parent
    
    # Define processor scripts
    vision_processor = current_dir / "file_vision_processor.py"
    ffmpeg_processor = current_dir / "file_ffmpeg_processor.py"
    
    # Check if scripts exist
    if not vision_processor.exists():
        print(f"❌ Vision processor not found: {vision_processor}")
        return False
    
    if not ffmpeg_processor.exists():
        print(f"❌ FFmpeg processor not found: {ffmpeg_processor}")
        return False
    
    print(f"✅ Vision processor: {vision_processor}")
    print(f"✅ FFmpeg processor: {ffmpeg_processor}")
    
    try:
        # Start vision processor
        print("\n🔍 Starting Vision Processor...")
        vision_process = subprocess.Popen([
            sys.executable, str(vision_processor)
        ], cwd=str(current_dir))
        
        print(f"✅ Vision processor started (PID: {vision_process.pid})")
        
        # Start FFmpeg processor
        print("\n🎬 Starting FFmpeg Processor...")
        ffmpeg_process = subprocess.Popen([
            sys.executable, str(ffmpeg_processor)
        ], cwd=str(current_dir))
        
        print(f"✅ FFmpeg processor started (PID: {ffmpeg_process.pid})")
        
        print("\n🎯 Both processors are running!")
        print("📁 Monitoring folders:")
        print("   - Vision requests: C:/Docker_Share/N8N/vision_requests")
        print("   - Vision results: C:/Docker_Share/N8N/vision_results")
        print("   - FFmpeg requests: C:/Docker_Share/N8N/ffmpeg_requests")
        print("   - FFmpeg results: C:/Docker_Share/N8N/ffmpeg_results")
        print("\n💡 Press Ctrl+C to stop both processors")
        
        # Wait for processes
        try:
            while True:
                # Check if processes are still running
                vision_running = vision_process.poll() is None
                ffmpeg_running = ffmpeg_process.poll() is None
                
                if not vision_running:
                    print("⚠️  Vision processor stopped unexpectedly")
                    break
                
                if not ffmpeg_running:
                    print("⚠️  FFmpeg processor stopped unexpectedly")
                    break
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping processors...")
            
            # Terminate processes
            if vision_process.poll() is None:
                vision_process.terminate()
                print("✅ Vision processor stopped")
            
            if ffmpeg_process.poll() is None:
                ffmpeg_process.terminate()
                print("✅ FFmpeg processor stopped")
            
            print("🏁 All processors stopped")
            
    except Exception as e:
        print(f"❌ Error starting processors: {e}")
        return False
    
    return True

def main():
    """Main function."""
    print("Kung Fu Video Detection - File Processors")
    print("=========================================")
    
    success = start_processors()
    
    if not success:
        print("\n❌ Failed to start processors")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
